import { ConfigContext } from "@embrace-lib/common";
import { useTheme } from "@embrace-lib/theming";
import type { ExternalComponentProps, TranslatableAutoSelectOption } from "@embrace-shared/forms";
import { GenericError, Loading, ScrollBar } from "@embrace-shared/main";
import {
  portalsFormsTranslations as f,
  globalTranslations as g,
  Namespace,
  useTranslate,
} from "@embrace-shared/translations";
import Autocomplete from "@embracesbs/component-autocomplete";
import Container from "@embracesbs/component-container";
import List from "@embracesbs/component-list";
import TextFieldWrapper from "@embracesbs/component-textfieldwrapper";
import { useCallback, useContext, useMemo } from "react";
import { useParams } from "react-router-dom";
import type { AppStoreRouteParams } from "../../../../types";
import useWidgetQuery from "../../useWidgetQuery";
import usePortalsQuery from "./hooks/usePortalsQuery";
import PortalsListItem from "./PortalsListItem/PortalsListItem";

type PortalListData = {
  notYetSelected: TranslatableAutoSelectOption<string>[];
  selected: TranslatableAutoSelectOption<string>[];
};

const PortalsList: React.FC<ExternalComponentProps> = ({ name, onChange, value }) => {
  const theme = useTheme();
  const { widgetId } = useParams<AppStoreRouteParams>();
  const translate = useTranslate(Namespace.global, Namespace.portalsForms);
  const { tenantName } = useContext(ConfigContext);
  const { allowedWidget } = useWidgetQuery(widgetId || "");

  const { portals, loading, error } = usePortalsQuery(tenantName, allowedWidget?.portalTypes ?? []);
  const { selected, notYetSelected } = portals.reduce(
    (acc, item) => {
      if (((value || []) as string[]).includes(item.value)) {
        return {
          ...acc,
          selected: acc.selected.concat(item),
        };
      }

      return {
        ...acc,
        notYetSelected: acc.notYetSelected.concat(item),
      };
    },
    {
      notYetSelected: [],
      selected: [],
    } as PortalListData
  );

  const handleOnAddPortal = (newPortal: TranslatableAutoSelectOption<string>): void => {
    onChange(name, [...((value || []) as string[]), newPortal.value]);
  };

  const handleOnRemove = useCallback(
    (portalForRemove: TranslatableAutoSelectOption<string>): void => {
      onChange(
        name,
        ((value || []) as string[]).filter((item) => item !== portalForRemove.value)
      );
    },
    [name, onChange, value]
  );

  const renderedSelectedPortals = useMemo(() => {
    if (!selected) {
      return null;
    }

    return selected.map((portal) => {
      return <PortalsListItem key={portal.value} optionItem={portal} onRemove={handleOnRemove} />;
    });
  }, [handleOnRemove, selected]);

  if (loading) {
    return <Loading testId="appstore-portals-loading" />;
  }

  if (error) {
    return <GenericError message={translate(g.ErrorMessageFetchingDataText)} />;
  }

  return (
    <ScrollBar style={{ height: "500px" }}>
      <Container isVertical align="left" spacing={{ left: theme.spacing.$2Number, right: theme.spacing.$4Number }}>
        <TextFieldWrapper labelText={translate(f.AutocompletePortalsListTitleLabel)} data-testid="insert-portals">
          <Autocomplete
            placeholder={translate(f.AutocompletePortalsListPlaceholder)}
            options={notYetSelected}
            value={[]}
            onChange={handleOnAddPortal}
            extraProps={{ Root: { menuPortalTarget: document.body } }}
          />
        </TextFieldWrapper>
        <Container isVertical>
          <List>{renderedSelectedPortals}</List>
        </Container>
      </Container>
    </ScrollBar>
  );
};

export default PortalsList;
