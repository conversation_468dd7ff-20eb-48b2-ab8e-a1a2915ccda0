/* This file is generated during start and build commands */
import { lazy, type ComponentType, type LazyExoticComponent } from "react";

/* eslint-disable-next-line */
type WidgetLazyComponent = LazyExoticComponent<ComponentType<any>>; // NOSONAR

const widgetMapper: Record<string, WidgetLazyComponent> = {
  accordion: lazy(() => import("@embrace-widgets/accordion/src/index")),
  accountabilityv2: lazy(() => import("@embrace-widgets/accountabilityv2/src/index")),
  actionnotification: lazy(() => import("@embrace-widgets/actionnotification/src/index")),
  analytics: lazy(() => import("@embrace-widgets/analytics/src/index")),
  announcement: lazy(() => import("@embrace-widgets/announcement/src/index")),
  attributes: lazy(() => import("@embrace-widgets/attributes/src/index")),
  banner: lazy(() => import("@embrace-widgets/banner/src/index")),
  bookmarks: lazy(() => import("@embrace-widgets/bookmarks/src/index")),
  breadcrumbnavigation: lazy(() => import("@embrace-widgets/breadcrumbnavigation/src/index")),
  calendar: lazy(() => import("@embrace-widgets/calendar/src/index")),
  celebrations: lazy(() => import("@embrace-widgets/celebrations/src/index")),
  changepassword: lazy(() => import("@embrace-widgets/changepassword/src/index")),
  changeusername: lazy(() => import("@embrace-widgets/changeusername/src/index")),
  colleagues: lazy(() => import("@embrace-widgets/colleagues/src/index")),
  comments: lazy(() => import("@embrace-widgets/comments/src/index")),
  communicationpreferences: lazy(() => import("@embrace-widgets/communicationpreferences/src/index")),
  contact: lazy(() => import("@embrace-widgets/contact/src/index")),
  contactcards: lazy(() => import("@embrace-widgets/contactcards/src/index")),
  contentcards: lazy(() => import("@embrace-widgets/contentcards/src/index")),
  contentproperties: lazy(() => import("@embrace-widgets/contentproperties/src/index")),
  contentreviewsummary: lazy(() => import("@embrace-widgets/contentreviewsummary/src/index")),
  contractdetail: lazy(() => import("@embrace-widgets/contractdetail/src/index")),
  contractoverviewv2: lazy(() => import("@embrace-widgets/contractoverviewv2/src/index")),
  cotenants: lazy(() => import("@embrace-widgets/cotenants/src/index")),
  demorequiredreading: lazy(() => import("@embrace-widgets/demorequiredreading/src/index")),
  deregister: lazy(() => import("@embrace-widgets/deregister/src/index")),
  extraregistrationquestionsform: lazy(() => import("@embrace-widgets/extraregistrationquestionsform/src/index")),
  faqdetail: lazy(() => import("@embrace-widgets/faqdetail/src/index")),
  files: lazy(() => import("@embrace-widgets/files/src/index")),
  focus: lazy(() => import("@embrace-widgets/focus/src/index")),
  footerlinks: lazy(() => import("@embrace-widgets/footerlinks/src/index")),
  footersocials: lazy(() => import("@embrace-widgets/footersocials/src/index")),
  gdpr: lazy(() => import("@embrace-widgets/gdpr/src/index")),
  greeting: lazy(() => import("@embrace-widgets/greeting/src/index")),
  groupinfo: lazy(() => import("@embrace-widgets/groupinfo/src/index")),
  header: lazy(() => import("@embrace-widgets/header/src/index")),
  householdadd: lazy(() => import("@embrace-widgets/householdadd/src/index")),
  householddetail: lazy(() => import("@embrace-widgets/householddetail/src/index")),
  householdoverviewv2: lazy(() => import("@embrace-widgets/householdoverviewv2/src/index")),
  householdsummary: lazy(() => import("@embrace-widgets/householdsummary/src/index")),
  housinguserstatusmodal: lazy(() => import("@embrace-widgets/housinguserstatusmodal/src/index")),
  impersonationnotice: lazy(() => import("@embrace-widgets/impersonationnotice/src/index")),
  incomeform: lazy(() => import("@embrace-widgets/incomeform/src/index")),
  invoicesoverview: lazy(() => import("@embrace-widgets/invoicesoverview/src/index")),
  knowledgebasedetail: lazy(() => import("@embrace-widgets/knowledgebasedetail/src/index")),
  knowledgebasedetailfaq: lazy(() => import("@embrace-widgets/knowledgebasedetailfaq/src/index")),
  knowledgebaseoverview: lazy(() => import("@embrace-widgets/knowledgebaseoverview/src/index")),
  knowledgedetail: lazy(() => import("@embrace-widgets/knowledgedetail/src/index")),
  knowledgefilters: lazy(() => import("@embrace-widgets/knowledgefilters/src/index")),
  knowledgeoverview: lazy(() => import("@embrace-widgets/knowledgeoverview/src/index")),
  knowledgerelatedcontent: lazy(() => import("@embrace-widgets/knowledgerelatedcontent/src/index")),
  knowledgerelatedconversations: lazy(() => import("@embrace-widgets/knowledgerelatedconversations/src/index")),
  linkediconnavigationmenu: lazy(() => import("@embrace-widgets/linkediconnavigationmenu/src/index")),
  liquit: lazy(() => import("@embrace-widgets/liquit/src/index")),
  livingpreferencesform: lazy(() => import("@embrace-widgets/livingpreferencesform/src/index")),
  livingpreferenceswarning: lazy(() => import("@embrace-widgets/livingpreferenceswarning/src/index")),
  livingsituationform: lazy(() => import("@embrace-widgets/livingsituationform/src/index")),
  media: lazy(() => import("@embrace-widgets/media/src/index")),
  microsoftoutlookcalendar: lazy(() => import("@embrace-widgets/microsoftoutlookcalendar/src/index")),
  microsoftoutlookemail: lazy(() => import("@embrace-widgets/microsoftoutlookemail/src/index")),
  microsoftteams: lazy(() => import("@embrace-widgets/microsoftteams/src/index")),
  microsofttodo: lazy(() => import("@embrace-widgets/microsofttodo/src/index")),
  mostusedtags: lazy(() => import("@embrace-widgets/mostusedtags/src/index")),
  mycaseflowdetail: lazy(() => import("@embrace-widgets/mycaseflowdetail/src/index")),
  mycaseflowsoverviewv2: lazy(() => import("@embrace-widgets/mycaseflowsoverviewv2/src/index")),
  mycaseflowssummary: lazy(() => import("@embrace-widgets/mycaseflowssummary/src/index")),
  mycontracts: lazy(() => import("@embrace-widgets/mycontracts/src/index")),
  mygroups: lazy(() => import("@embrace-widgets/mygroups/src/index")),
  mypinnedgroups: lazy(() => import("@embrace-widgets/mypinnedgroups/src/index")),
  narrowcasting: lazy(() => import("@embrace-widgets/narrowcasting/src/index")),
  narrowcastingframe: lazy(() => import("@embrace-widgets/narrowcastingframe/src/index")),
  navigationbar: lazy(() => import("@embrace-widgets/navigationbar/src/index")),
  navigationcard: lazy(() => import("@embrace-widgets/navigationcard/src/index")),
  navigationcards: lazy(() => import("@embrace-widgets/navigationcards/src/index")),
  neighborhoodmanagers: lazy(() => import("@embrace-widgets/neighborhoodmanagers/src/index")),
  newsdetail: lazy(() => import("@embrace-widgets/newsdetail/src/index")),
  newsfilters: lazy(() => import("@embrace-widgets/newsfilters/src/index")),
  newshero: lazy(() => import("@embrace-widgets/newshero/src/index")),
  newsoverview: lazy(() => import("@embrace-widgets/newsoverview/src/index")),
  notes: lazy(() => import("@embrace-widgets/notes/src/index")),
  notificationpreferences: lazy(() => import("@embrace-widgets/notificationpreferences/src/index")),
  onboarding: lazy(() => import("@embrace-widgets/onboarding/src/index")),
  outofoffice: lazy(() => import("@embrace-widgets/outofoffice/src/index")),
  outstandingbalance: lazy(() => import("@embrace-widgets/outstandingbalance/src/index")),
  ownerinformation: lazy(() => import("@embrace-widgets/ownerinformation/src/index")),
  pages: lazy(() => import("@embrace-widgets/pages/src/index")),
  passwordforgotten: lazy(() => import("@embrace-widgets/passwordforgotten/src/index")),
  payinvoice: lazy(() => import("@embrace-widgets/payinvoice/src/index")),
  paymentstatus: lazy(() => import("@embrace-widgets/paymentstatus/src/index")),
  personaldetails: lazy(() => import("@embrace-widgets/personaldetails/src/index")),
  photogallery: lazy(() => import("@embrace-widgets/photogallery/src/index")),
  planning: lazy(() => import("@embrace-widgets/planning/src/index")),
  polls: lazy(() => import("@embrace-widgets/polls/src/index")),
  populargroups: lazy(() => import("@embrace-widgets/populargroups/src/index")),
  popularposts: lazy(() => import("@embrace-widgets/popularposts/src/index")),
  portalcontenthero: lazy(() => import("@embrace-widgets/portalcontenthero/src/index")),
  portalcounter: lazy(() => import("@embrace-widgets/portalcounter/src/index")),
  portalfiles: lazy(() => import("@embrace-widgets/portalfiles/src/index")),
  portalheader: lazy(() => import("@embrace-widgets/portalheader/src/index")),
  portalinbox: lazy(() => import("@embrace-widgets/portalinbox/src/index")),
  portalinboxsummary: lazy(() => import("@embrace-widgets/portalinboxsummary/src/index")),
  portalknowledgearticle: lazy(() => import("@embrace-widgets/portalknowledgearticle/src/index")),
  portalknowledgedetail: lazy(() => import("@embrace-widgets/portalknowledgedetail/src/index")),
  portalknowledgeoverviewv2: lazy(() => import("@embrace-widgets/portalknowledgeoverviewv2/src/index")),
  portalknowledgetopic: lazy(() => import("@embrace-widgets/portalknowledgetopic/src/index")),
  portalmedia: lazy(() => import("@embrace-widgets/portalmedia/src/index")),
  portalnavigation: lazy(() => import("@embrace-widgets/portalnavigation/src/index")),
  portalnewsdetail: lazy(() => import("@embrace-widgets/portalnewsdetail/src/index")),
  portalnewsoverviewv2: lazy(() => import("@embrace-widgets/portalnewsoverviewv2/src/index")),
  portalprofilesummary: lazy(() => import("@embrace-widgets/portalprofilesummary/src/index")),
  portalrecentnewsv2: lazy(() => import("@embrace-widgets/portalrecentnewsv2/src/index")),
  portalrelatedconversations: lazy(() => import("@embrace-widgets/portalrelatedconversations/src/index")),
  portalrelatednews: lazy(() => import("@embrace-widgets/portalrelatednews/src/index")),
  portalrelatedtopics: lazy(() => import("@embrace-widgets/portalrelatedtopics/src/index")),
  portalshortcuttiles: lazy(() => import("@embrace-widgets/portalshortcuttiles/src/index")),
  portalsingleconversation: lazy(() => import("@embrace-widgets/portalsingleconversation/src/index")),
  portalsubjectcards: lazy(() => import("@embrace-widgets/portalsubjectcards/src/index")),
  portaltext: lazy(() => import("@embrace-widgets/portaltext/src/index")),
  privacypreferences: lazy(() => import("@embrace-widgets/privacypreferences/src/index")),
  profilestatus: lazy(() => import("@embrace-widgets/profilestatus/src/index")),
  projectamenities: lazy(() => import("@embrace-widgets/projectamenities/src/index")),
  projectfacilities: lazy(() => import("@embrace-widgets/projectfacilities/src/index")),
  projectmedia: lazy(() => import("@embrace-widgets/projectmedia/src/index")),
  projectplanning: lazy(() => import("@embrace-widgets/projectplanning/src/index")),
  projecttitle: lazy(() => import("@embrace-widgets/projecttitle/src/index")),
  publicationdetail: lazy(() => import("@embrace-widgets/publicationdetail/src/index")),
  publicationdetailmedia: lazy(() => import("@embrace-widgets/publicationdetailmedia/src/index")),
  publicationdetailrequirements: lazy(() => import("@embrace-widgets/publicationdetailrequirements/src/index")),
  publicationfilter: lazy(() => import("@embrace-widgets/publicationfilter/src/index")),
  publicationlistv2: lazy(() => import("@embrace-widgets/publicationlistv2/src/index")),
  publicationmap: lazy(() => import("@embrace-widgets/publicationmap/src/index")),
  publicationpointsofinterest: lazy(() => import("@embrace-widgets/publicationpointsofinterest/src/index")),
  publicationreaction: lazy(() => import("@embrace-widgets/publicationreaction/src/index")),
  publicationsurfaces: lazy(() => import("@embrace-widgets/publicationsurfaces/src/index")),
  questions: lazy(() => import("@embrace-widgets/questions/src/index")),
  reactionssummary: lazy(() => import("@embrace-widgets/reactionssummary/src/index")),
  recentchanges: lazy(() => import("@embrace-widgets/recentchanges/src/index")),
  recentnews: lazy(() => import("@embrace-widgets/recentnews/src/index")),
  recoverpassword: lazy(() => import("@embrace-widgets/recoverpassword/src/index")),
  registration: lazy(() => import("@embrace-widgets/registration/src/index")),
  registrationfollowup: lazy(() => import("@embrace-widgets/registrationfollowup/src/index")),
  relatedinfo: lazy(() => import("@embrace-widgets/relatedinfo/src/index")),
  relatedlinks: lazy(() => import("@embrace-widgets/relatedlinks/src/index")),
  relatedpages: lazy(() => import("@embrace-widgets/relatedpages/src/index")),
  relatedselfservicescenariosv2: lazy(() => import("@embrace-widgets/relatedselfservicescenariosv2/src/index")),
  repairrequest: lazy(() => import("@embrace-widgets/repairrequest/src/index")),
  repairrequestdetail: lazy(() => import("@embrace-widgets/repairrequestdetail/src/index")),
  repairrequestoverview: lazy(() => import("@embrace-widgets/repairrequestoverview/src/index")),
  rss: lazy(() => import("@embrace-widgets/rss/src/index")),
  schedule: lazy(() => import("@embrace-widgets/schedule/src/index")),
  screentitle: lazy(() => import("@embrace-widgets/screentitle/src/index")),
  search: lazy(() => import("@embrace-widgets/search/src/index")),
  searchdetail: lazy(() => import("@embrace-widgets/searchdetail/src/index")),
  sectioncard: lazy(() => import("@embrace-widgets/sectioncard/src/index")),
  shortcuts: lazy(() => import("@embrace-widgets/shortcuts/src/index")),
  shortcutsv2: lazy(() => import("@embrace-widgets/shortcutsv2/src/index")),
  singleconversation: lazy(() => import("@embrace-widgets/singleconversation/src/index")),
  singleselfservicescenario: lazy(() => import("@embrace-widgets/singleselfservicescenario/src/index")),
  streetmaps: lazy(() => import("@embrace-widgets/streetmaps/src/index")),
  tabbednavigation: lazy(() => import("@embrace-widgets/tabbednavigation/src/index")),
  tenancyapplicationdetail: lazy(() => import("@embrace-widgets/tenancyapplicationdetail/src/index")),
  tenancyapplicationoverviewv2: lazy(() => import("@embrace-widgets/tenancyapplicationoverviewv2/src/index")),
  text: lazy(() => import("@embrace-widgets/text/src/index")),
  timeline: lazy(() => import("@embrace-widgets/timeline/src/index")),
  topdesk: lazy(() => import("@embrace-widgets/topdesk/src/index")),
  twofactorauthentication: lazy(() => import("@embrace-widgets/twofactorauthentication/src/index")),
  unauthenticated: lazy(() => import("@embrace-widgets/unauthenticated/src/index")),
  verifyusername: lazy(() => import("@embrace-widgets/verifyusername/src/index")),
  video: lazy(() => import("@embrace-widgets/video/src/index")),
  weather: lazy(() => import("@embrace-widgets/weather/src/index")),
  weatherforecast: lazy(() => import("@embrace-widgets/weatherforecast/src/index")),
  weatherrainfall: lazy(() => import("@embrace-widgets/weatherrainfall/src/index")),
  whatsappchat: lazy(() => import("@embrace-widgets/whatsappchat/src/index")),
  zenyadocuments: lazy(() => import("@embrace-widgets/zenyadocuments/src/index")),
  zenyarelateddocuments: lazy(() => import("@embrace-widgets/zenyarelateddocuments/src/index")),
  zenyatasks: lazy(() => import("@embrace-widgets/zenyatasks/src/index")),
};

export default widgetMapper;
