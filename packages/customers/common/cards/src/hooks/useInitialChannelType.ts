import { useContextState } from "@embrace-lib/common";
import { PhoneCallStatus, TelephonyContext, type PhoneCall } from "@embrace-shared/customers-telephony";
import { ChannelTypeInput } from "@embrace-shared/graphql";
import { useEffect, useMemo, useState } from "react";
import { CardContext } from "../components/CardContext";

const getChannelTypeByRoute = (): ChannelTypeInput | undefined => {
  const path = window.location.pathname;

  if (path.includes("/chat/")) return ChannelTypeInput.Chat;

  if (path.includes("/email/")) return ChannelTypeInput.Email;

  return undefined;
};

const ActiveCallStatusTypes: PhoneCallStatus[] = [
  PhoneCallStatus.Established,
  PhoneCallStatus.OnHold,
  PhoneCallStatus.Acw,
];

const useInitialChannelType = (): ChannelTypeInput | undefined => {
  const { id, type } = useContextState(CardContext);
  const { calls, selectedCall, firstActiveCall } = useContextState(TelephonyContext);

  const [initialChannel, setInitialChannel] = useState<ChannelTypeInput | undefined>();

  const isCustomerCard = type === "customer";
  const linkedPersonCall = calls.find((call: PhoneCall) => call.linkedPerson?.id === id);
  const isLinkedCallActive = ActiveCallStatusTypes.some((status) => status === linkedPersonCall?.status);
  const isCallActiveForCustomer = isLinkedCallActive && isCustomerCard;
  const activePhoneCall = selectedCall || firstActiveCall;

  const automaticallyDeterminedChannelType = useMemo(() => {
    if (activePhoneCall) return ChannelTypeInput.Phone;

    return getChannelTypeByRoute();
  }, [activePhoneCall]);

  useEffect(() => {
    if (automaticallyDeterminedChannelType) {
      setInitialChannel(automaticallyDeterminedChannelType);
    }

    if (isCallActiveForCustomer) {
      setInitialChannel(ChannelTypeInput.Phone);
    }
  }, [isCallActiveForCustomer, automaticallyDeterminedChannelType]);

  return initialChannel;
};

export default useInitialChannelType;
