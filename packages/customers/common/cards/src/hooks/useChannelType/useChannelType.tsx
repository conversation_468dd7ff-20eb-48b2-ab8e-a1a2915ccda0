import { ChannelTypeInput } from "@embrace-shared/graphql";
import { Namespace, cardsTicketsTranslations as t1, useTranslate } from "@embrace-shared/translations";
import type { IconNames } from "@embracesbs/component-icon";
import { useEffect, useState } from "react";
import { useCardsChannelTypeCachingQuery } from "./__generated__/cardsChannelTypeCaching.types";

const TICKETS_CREATE_CHANNEL_LS_KEY = "ticket-create-channel";

export const getChannelTypeIcon = (channel: ChannelTypeInput | string): IconNames => {
  // casting to lowercase here because channel enum isn't used consistent in the graph
  switch (channel.toLowerCase()) {
    case ChannelTypeInput.Email.toLowerCase():
      return "EmailActionUnread";
    case ChannelTypeInput.Sms.toLowerCase():
      return "MessagesBubbleSquareText";
    case ChannelTypeInput.Phone.toLowerCase():
      return "Phone";
    case ChannelTypeInput.Chat.toLowerCase():
      return "ConversationChatText";
    case ChannelTypeInput.Backoffice.toLowerCase():
      return "TeamMeeting";
    case ChannelTypeInput.Desk.toLowerCase():
      return "InformationDeskMan";
    case ChannelTypeInput.Post.toLowerCase():
      return "MailboxIn";
    case ChannelTypeInput.Social.toLowerCase():
      return "SocialProfileNetwork";
    case ChannelTypeInput.Survey.toLowerCase():
      return "MessagesPeopleUserQuestion";
    case ChannelTypeInput.Visit.toLowerCase():
      return "HouseUser";
    case ChannelTypeInput.Web.toLowerCase():
      return "Hierarchy2";
    default:
      return "StrategySplit";
  }
};

type ChannelType = { value: ChannelTypeInput; label: string };
type SelectedValue = { label: string; value: ChannelTypeInput };

const useChannelType = (
  initialChannel: ChannelTypeInput
): {
  channelOptions: ChannelType[];
  selectedChannel: ChannelTypeInput | undefined;
  handleChannelChange: (x: SelectedValue) => void;
} => {
  const translate = useTranslate(Namespace.cardsTickets);
  const [selectedChannel, setSelectedChannel] = useState<ChannelTypeInput | undefined>(initialChannel);
  const { data } = useCardsChannelTypeCachingQuery({
    fetchPolicy: "cache-first",
  });
  const isChannelTypeCachingEnabled = data?.config.tickets.isChannelTypeCachingEnabled ?? false;

  const channelOptions: ChannelType[] = [
    { value: ChannelTypeInput.Desk, label: translate(t1.AutocompleteChannelDeskOptions) },
    { value: ChannelTypeInput.Visit, label: translate(t1.AutocompleteChannelVisitOptions) },
    { value: ChannelTypeInput.Phone, label: translate(t1.AutocompleteChannelPhoneOptions) },
    { value: ChannelTypeInput.Email, label: translate(t1.AutocompleteChannelEmailOptions) },
    { value: ChannelTypeInput.Chat, label: translate(t1.AutocompleteChannelChatOptions) },
    { value: ChannelTypeInput.Post, label: translate(t1.AutocompleteChannelPostOptions) },
    { value: ChannelTypeInput.Social, label: translate(t1.AutocompleteChannelSocialOptions) },
    { value: ChannelTypeInput.Survey, label: translate(t1.AutocompleteChannelSurveyOptions) },
    { value: ChannelTypeInput.Backoffice, label: translate(t1.AutocompleteChannelBackofficeOptions) },
  ];

  const handleChannelChange = (selectValue: SelectedValue): void => {
    setSelectedChannel(selectValue?.value ?? undefined);

    if (!isChannelTypeCachingEnabled) return;

    if (selectValue) localStorage.setItem(TICKETS_CREATE_CHANNEL_LS_KEY, JSON.stringify(selectValue));
    else localStorage.removeItem(TICKETS_CREATE_CHANNEL_LS_KEY);
  };

  const getChannelTypeFromCache = (): ChannelType | undefined => {
    const ls = localStorage.getItem(TICKETS_CREATE_CHANNEL_LS_KEY);
    if (!ls) return undefined;

    const previousChannel: { value: ChannelTypeInput; label: string } = JSON.parse(ls);
    return previousChannel;
  };

  useEffect(() => {
    if (!initialChannel) return;

    setSelectedChannel(initialChannel);
  }, [initialChannel]);

  useEffect(() => {
    if (selectedChannel) return;

    if (!isChannelTypeCachingEnabled) {
      return;
    }

    const lastSelectedChannelType = getChannelTypeFromCache();
    if (lastSelectedChannelType) setSelectedChannel(lastSelectedChannelType.value);
  }, [isChannelTypeCachingEnabled, selectedChannel]);

  return { channelOptions, handleChannelChange, selectedChannel };
};

export default useChannelType;
