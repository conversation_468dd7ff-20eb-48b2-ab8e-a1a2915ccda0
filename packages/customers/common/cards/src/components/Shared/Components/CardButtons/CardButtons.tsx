import { WindowsContext } from "@embrace-customers-shared/windows";
import { FeatureName, useContextActions, useContextState, useFeature } from "@embrace-lib/common";
import { useTheme } from "@embrace-lib/theming";
import { TelephonyCallButton, TelephonyContext, useActivePhoneCall } from "@embrace-shared/customers-telephony";
import { PhoneType } from "@embrace-shared/graphql";
import { useLayoutInformation } from "@embrace-shared/layout";
import {
  ResponsiveButtons,
  usePermissions,
  type ResponsiveButtonConfig,
  type ResponsiveButtonRenderConfig,
} from "@embrace-shared/main";
import {
  cardsCustomerCardTranslations,
  cardsTranslations,
  Namespace,
  useTranslate,
} from "@embrace-shared/translations";
import React, { useCallback, useMemo } from "react";
import { useParams } from "react-router-dom";
import { type CardType } from "../../../../cards.types";
import useInitialChannelType from "../../../../hooks/useInitialChannelType";
import { CardContext, TAB_ACTIVE_ITEM_ID_DEFAULT } from "../../../CardContext";
import { ViewsEnum } from "../TicketWindow/TicketWindow.types";
import ColdTransferCallButton from "./ColdTransferCallButton";
import EmailLinkButton from "./EmailLinkButton";
import { handleOpenGoogleMaps } from "./helpers";
import handleEmailOpen from "./helpers/handleOpenEmail";
import RelationLinkButton from "./RelationLinkButton";
import TelephonyLinkButton from "./TelephonyLinkButton";

export type ButtonType =
  | "email"
  | "startCall"
  | "coldTransferCall"
  | "location"
  | "chatLink"
  | "telephonyLink"
  | "emailLink"
  | "addTicket"
  | "addAnnouncement";

export type ButtonsType = ButtonType[];

export interface CardButtonOptionInternal {
  icon: string;
  text: string;
  dataTestId?: string;
  onClick: () => void;
}

export interface CardButtonInternal extends CardButtonOptionInternal {
  options?: CardButtonOptionInternal[];
}

interface Props {
  buttons: ButtonsType;
  phoneNumbers: { number: string; type: PhoneType }[];
  email: string;
  mapAddress?: string;
  title: string;
  isCompact?: boolean;
  type: CardType;
}

const CardButtons: React.FC<Props> = ({
  buttons: allowedButtonTypes,
  email,
  phoneNumbers,
  mapAddress,
  title,
  isCompact = false,
  type,
}) => {
  const translate = useTranslate(Namespace.cards, Namespace.cardsCustomerCard);
  const theme = useTheme();
  const { setActiveItem, updateTab } = useContextActions(CardContext);
  const { hasPermission, permissions } = usePermissions();
  const { calls } = useContextState(TelephonyContext);
  const activeCall = useActivePhoneCall(calls);
  const column = useLayoutInformation();
  const isContextMode = column === "context";
  const { createWindow } = useContextActions(WindowsContext);
  const { id } = useParams();
  const hasCaseCreatePermission = hasPermission(permissions.caseCreate);
  const hasAnnouncementWritePermission = hasPermission(permissions.announcementWrite);
  const initialChannel = useInitialChannelType();
  console.log("🚀 ~ initialChannel2:", initialChannel);

  const actionButtonSize = theme.iconSize.$4Number;

  const useNewTicketCreationFlow = useFeature(FeatureName.UseNewTicketCreationFlow);

  const renderStartCall = useCallback<ResponsiveButtonRenderConfig>(
    (isNestedInMore) => {
      return (
        <TelephonyCallButton
          key="startCall"
          phoneNumbers={phoneNumbers}
          isNestedInMore={isNestedInMore}
          size={actionButtonSize}
        />
      );
    },
    [actionButtonSize, phoneNumbers]
  );

  const renderColdTransferCall = useCallback<ResponsiveButtonRenderConfig>(
    (isNestedInMore) => {
      return (
        <ColdTransferCallButton
          key="coldTransferCall"
          phoneNumbers={phoneNumbers}
          isNestedInMore={isNestedInMore}
          size={actionButtonSize}
        />
      );
    },
    [actionButtonSize, phoneNumbers]
  );

  const renderChatLink = useCallback<ResponsiveButtonRenderConfig>(
    (isNestedInMore) => <RelationLinkButton key="chatLink" isNestedInMore={isNestedInMore} size={actionButtonSize} />,
    [actionButtonSize]
  );

  const renderTelephonyLink = useCallback<ResponsiveButtonRenderConfig>(
    (isNestedInMore) => (
      <TelephonyLinkButton
        key="telephonyLink"
        isNestedInMore={isNestedInMore}
        size={actionButtonSize}
        fullName={title}
      />
    ),
    [actionButtonSize, title]
  );

  const renderEmailLink = useCallback<ResponsiveButtonRenderConfig>(
    (isNestedInMore) => <EmailLinkButton key="emailLink" isNestedInMore={isNestedInMore} size={actionButtonSize} />,
    [actionButtonSize]
  );

  const redirectToCustomerTickets = useCallback(() => {
    setActiveItem(TAB_ACTIVE_ITEM_ID_DEFAULT);
    updateTab("tickets");
  }, [setActiveItem, updateTab]);

  const openTicketCreationFlow = useCallback(() => {
    createWindow(
      {
        type: "TicketCreation",
        props: {
          openModal: true,
          cardType: type,
          initialChannel,
          windowType: ViewsEnum.CreateTicket,
          onSuccess: redirectToCustomerTickets,
          title,
          id,
        },
      },
      "TicketCreation",
      true
    );
  }, [createWindow, type, initialChannel, redirectToCustomerTickets, title, id]);

  const buttonsConfigs: ResponsiveButtonConfig[] = useMemo(
    () =>
      allowedButtonTypes.map((buttonType) => {
        switch (buttonType) {
          case "email": {
            return {
              id: "email",
              text: translate(cardsTranslations.ButtonMailCardHeaderText),
              iconName: "EmailFilled",
              listIconName: "EmailActionUnread",
              testId: "card-header-email-button",
              isDisabled: !email.length,
              isDisabledTooltip: translate(cardsTranslations.TooltipMailCardHeaderNoNumbersContent),
              onClick: (): void => handleEmailOpen(email),
            } as ResponsiveButtonConfig;
          }
          case "startCall": {
            return renderStartCall;
          }
          case "coldTransferCall": {
            if (!activeCall || !activeCall.canDirectTransfer) return null;

            return renderColdTransferCall;
          }
          case "location": {
            return {
              id: "location",
              text: translate(cardsTranslations.ButtonMapCardHeaderText),
              iconName: "MapsFilled",
              listIconName: "MapsPin1",
              onClick: () => handleOpenGoogleMaps(mapAddress),
              testId: "card-header-map-button",
            } as ResponsiveButtonConfig;
          }
          case "addTicket": {
            return hasCaseCreatePermission
              ? ({
                  id: "addTicket",
                  text: translate(cardsCustomerCardTranslations.ButtonCardHeaderAddTicketText),
                  iconName: "TicketFilled",
                  listIconName: "Ticket",
                  onClick: () =>
                    useNewTicketCreationFlow?.enabled
                      ? openTicketCreationFlow()
                      : setActiveItem("knowledgebase", "create-ticket"),
                  testId: "add-ticket",
                  isActive: true,
                } as ResponsiveButtonConfig)
              : null;
          }
          case "addAnnouncement": {
            return hasAnnouncementWritePermission
              ? ({
                  id: "addAnnouncement",
                  text: translate(cardsCustomerCardTranslations.ButtonCardHeaderAnnouncementText),
                  iconName: "MegaphoneFilled",
                  listIconName: "Megaphone",
                  onClick: () => setActiveItem("new-announcement"),
                  testId: "add-announcement-toggle",
                } as ResponsiveButtonConfig)
              : null;
          }
          case "chatLink": {
            return renderChatLink;
          }
          case "telephonyLink": {
            return renderTelephonyLink;
          }
          case "emailLink": {
            return renderEmailLink;
          }
          default:
            return null;
        }
      }),
    [
      activeCall,
      allowedButtonTypes,
      email,
      hasAnnouncementWritePermission,
      hasCaseCreatePermission,
      mapAddress,
      openTicketCreationFlow,
      renderChatLink,
      renderColdTransferCall,
      renderEmailLink,
      renderStartCall,
      renderTelephonyLink,
      setActiveItem,
      translate,
      useNewTicketCreationFlow,
    ]
  );

  return (
    <ResponsiveButtons
      buttons={buttonsConfigs}
      moreActionsIconName="NavigationMenuHorizontalFilled"
      testId="card-buttons"
      spacing={isCompact ? undefined : { top: theme.spacing.$6Number }}
      justify={isContextMode ? "center" : "flex-start"}
    />
  );
};

export default CardButtons;
