import { ChannelTypeInput } from "@embrace-shared/graphql";
import { createContext, useContext, useMemo } from "react";
import type { CardType } from "../../../../cards.types";
import { SelectionTabEnum, type State, type WindowTicketTypes } from "./TicketWindow.types";

interface ProviderProps<T extends WindowTicketTypes> extends React.PropsWithChildren, TicketWindowContextType<T> {}

type TicketWindowContextType<T extends WindowTicketTypes> = {
  state: Partial<State<T>>;
  onContentChange(contentState: Partial<State<T>>): void;
  options: {
    isConversationFaq?: boolean;
    cardType: CardType;
    cardId: string;
    initialChannel: ChannelTypeInput | undefined;
  };
  closeWindow: () => void;
  setOnClose: (callback?: () => void) => void;
};

const TicketWindowContext = createContext<TicketWindowContextType<WindowTicketTypes>>({
  state: {
    activeStep: 0,
    activeTab: SelectionTabEnum.All,
  },
  onContentChange: () => {},
  options: {
    isConversationFaq: false,
    cardType: "customer",
    cardId: "",
    initialChannel: undefined,
  },
  closeWindow: () => {},
  setOnClose: () => {},
});

const TicketWindowProvider = <T extends WindowTicketTypes>({
  children,
  state,
  onContentChange,
  options,
  closeWindow,
  setOnClose,
}: ProviderProps<T>): JSX.Element => {
  const value = useMemo(
    () => ({ state, options, closeWindow, onContentChange, setOnClose }),
    [state, options, closeWindow, onContentChange, setOnClose]
  );

  return <TicketWindowContext.Provider value={value}>{children}</TicketWindowContext.Provider>;
};

const useTicketWindow = <T extends WindowTicketTypes>(): TicketWindowContextType<T> =>
  useContext(TicketWindowContext) as TicketWindowContextType<T>;

export { TicketWindowProvider, TicketWindowContext, useTicketWindow };
