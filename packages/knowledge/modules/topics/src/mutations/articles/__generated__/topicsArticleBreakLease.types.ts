/* eslint-disable */
/**
 * This file is auto generated
 * DO NOT MODIFY
 */

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
import type * as Types from "@embrace-shared/graphql";
import { TopicArticleFragmentDoc } from "../../../fragments/__generated__/topicArticle.types";

const defaultOptions = {} as const;
export type TopicsArticleBreakLeaseMutationVariables = Types.Exact<{
  id: Types.Scalars["ID"]["input"];
}>;

export type TopicsArticleBreakLeaseMutation = {
  __typename?: "Mutation";
  article?:
    | { __typename?: "ContentApplication" }
    | { __typename?: "ContentConfig" }
    | { __typename?: "ContentConversation" }
    | { __typename?: "ContentDistrict" }
    | { __typename?: "ContentGroup" }
    | { __typename?: "ContentGuide" }
    | { __typename?: "ContentJob" }
    | {
        __typename?: "ContentKnowledgeArticle";
        id: string;
        headerImageId?: string | null;
        version: number;
        tags: Array<string>;
        peopleIds?: Array<string> | null;
        groupsIds?: Array<string> | null;
        savedOn: any;
        zenyaCollectionIds?: Array<string> | null;
        isChecked?: boolean | null;
        checkDate?: any | null;
        articleApplications: string;
        path?: Array<
          | { __typename?: "ContentApplication"; id: string; version: number }
          | { __typename?: "ContentConfig"; id: string; version: number }
          | { __typename?: "ContentConversation"; id: string; version: number }
          | { __typename?: "ContentDistrict"; id: string; version: number }
          | { __typename?: "ContentGroup"; id: string; version: number }
          | { __typename?: "ContentGuide"; id: string; version: number }
          | { __typename?: "ContentJob"; id: string; version: number }
          | { __typename?: "ContentKnowledgeArticle"; id: string; version: number }
          | { __typename?: "ContentNeighborhood"; id: string; version: number }
          | { __typename?: "ContentNeighborhoodManager"; id: string; version: number }
          | { __typename?: "ContentNewsItem"; id: string; version: number }
          | { __typename?: "ContentNotification"; id: string; version: number }
          | { __typename?: "ContentProject"; id: string; version: number }
          | { __typename?: "ContentSegment"; id: string; version: number }
          | { __typename?: "ContentSmartlabel"; id: string; version: number }
          | { __typename?: "ContentTag"; id: string; version: number }
          | { __typename?: "ContentTopic"; id: string; version: number }
          | { __typename?: "ContentTown"; id: string; version: number }
          | { __typename?: "ContentWiki"; id: string; version: number }
          | null
        > | null;
        meta: {
          __typename?: "ContentMeta";
          status: Types.ContentStatus;
          canSaveDraft: boolean;
          canArchive: boolean;
          canDelete: boolean;
          canPublish: boolean;
          canUnpublish: boolean;
          canReorder: boolean;
          canRestore: boolean;
          canRead: boolean;
          canManage: boolean;
          lastModifiedOn: any;
          createdOn: any;
          publishedOn?: any | null;
          publishedFrom?: any | null;
          publishedUntil?: any | null;
          publishedVersion?: number | null;
          lastModifiedBy?: { __typename?: "User"; id: string } | null;
          createdBy?: { __typename?: "User"; id: string } | null;
          leasedBy?: {
            __typename?: "User";
            id: string;
            firstName?: string | null;
            lastName?: string | null;
            username?: string | null;
          } | null;
          history?: Array<{
            __typename?: "ContentHistory";
            timestamp: any;
            user?: { __typename?: "User"; id: string; username?: string | null } | null;
          } | null> | null;
        };
        savedBy?: { __typename?: "User"; id: string; firstName?: string | null; lastName?: string | null } | null;
        title: Array<{ __typename?: "LocalizedString"; text: string; locale: string }>;
        content: Array<{ __typename?: "LocalizedString"; text: string; locale: string }>;
        zenyaDocuments: {
          __typename?: "IDContentSectionedCollection";
          items?: Array<string> | null;
          sections?: Array<{
            __typename?: "IDContentSection";
            title: string;
            items?: Array<string> | null;
          } | null> | null;
        };
        links: Array<{
          __typename?: "LinkLocalizedContentSectionedCollection";
          locale: string;
          value: {
            __typename?: "LinkContentSectionedCollection";
            items?: Array<{ __typename?: "Link"; title?: string | null; isExternal: boolean; value: string }> | null;
            sections?: Array<{
              __typename?: "LinkContentSection";
              title: string;
              items?: Array<{ __typename?: "Link"; title?: string | null; isExternal: boolean; value: string }> | null;
            } | null> | null;
          };
        } | null>;
        attachments?: Array<{
          __typename?: "MediaFileLocalizedContentSectionedCollection";
          locale: string;
          value: {
            __typename?: "MediaFileContentSectionedCollection";
            items?: Array<{
              __typename?: "MediaFile";
              id: string;
              name?: string | null;
              path?: string | null;
              type?: string | null;
              title?: Array<{
                __typename?: "MediaLocalizedString";
                locale?: string | null;
                text?: string | null;
              } | null> | null;
            } | null> | null;
            sections?: Array<{
              __typename?: "MediaFileContentSection";
              title: string;
              items?: Array<{
                __typename?: "MediaFile";
                id: string;
                name?: string | null;
                path?: string | null;
                type?: string | null;
                title?: Array<{
                  __typename?: "MediaLocalizedString";
                  locale?: string | null;
                  text?: string | null;
                } | null> | null;
              } | null> | null;
            } | null> | null;
          };
        } | null> | null;
        attachmentsV2?: Array<{
          __typename?: "IMediaItemLocalizedContentSectionedCollection";
          locale: string;
          value: {
            __typename?: "IMediaItemContentSectionedCollection";
            items?: Array<
              | {
                  __typename?: "MediaFile";
                  name?: string | null;
                  path?: string | null;
                  type?: string | null;
                  id: string;
                  title?: Array<{
                    __typename?: "MediaLocalizedString";
                    locale?: string | null;
                    text?: string | null;
                  } | null> | null;
                }
              | {
                  __typename?: "MediaFolder";
                  name?: string | null;
                  path?: string | null;
                  id: string;
                  title?: Array<{
                    __typename?: "MediaLocalizedString";
                    locale?: string | null;
                    text?: string | null;
                  } | null> | null;
                }
              | { __typename?: "MediaProviderFile"; id: string }
              | { __typename?: "MediaProviderFolder"; id: string }
              | null
            > | null;
            sections?: Array<{
              __typename?: "IMediaItemContentSection";
              title: string;
              items?: Array<
                | {
                    __typename?: "MediaFile";
                    name?: string | null;
                    path?: string | null;
                    type?: string | null;
                    id: string;
                    title?: Array<{
                      __typename?: "MediaLocalizedString";
                      locale?: string | null;
                      text?: string | null;
                    } | null> | null;
                  }
                | {
                    __typename?: "MediaFolder";
                    name?: string | null;
                    path?: string | null;
                    id: string;
                    title?: Array<{
                      __typename?: "MediaLocalizedString";
                      locale?: string | null;
                      text?: string | null;
                    } | null> | null;
                  }
                | { __typename?: "MediaProviderFile"; id: string }
                | { __typename?: "MediaProviderFolder"; id: string }
                | null
              > | null;
            } | null> | null;
          };
        } | null> | null;
        note?: Array<{ __typename?: "LocalizedString"; text: string; locale: string }> | null;
        people?: Array<{
          __typename?: "User";
          id: string;
          profile?: {
            __typename?: "SocialPerson";
            id: string;
            fullName?: string | null;
            avatar?: { __typename?: "SocialAvatar"; id: string; externalUrl?: string | null } | null;
          } | null;
        } | null> | null;
        slugs?: Array<{ __typename?: "ContentSlug"; value: string; locale: string; isMain: boolean } | null> | null;
        contentOwners?: Array<{
          __typename?: "User";
          firstName?: string | null;
          id: string;
          lastName?: string | null;
          profile?: {
            __typename?: "SocialPerson";
            id: string;
            fullName?: string | null;
            jobTitle?: string | null;
            avatar?: { __typename?: "SocialAvatar"; id: string; externalUrl?: string | null } | null;
          } | null;
        } | null> | null;
      }
    | { __typename?: "ContentNeighborhood" }
    | { __typename?: "ContentNeighborhoodManager" }
    | { __typename?: "ContentNewsItem" }
    | { __typename?: "ContentNotification" }
    | { __typename?: "ContentProject" }
    | { __typename?: "ContentSegment" }
    | { __typename?: "ContentSmartlabel" }
    | { __typename?: "ContentTag" }
    | { __typename?: "ContentTopic" }
    | { __typename?: "ContentTown" }
    | { __typename?: "ContentWiki" }
    | null;
};

export const TopicsArticleBreakLeaseDocument = /*#__PURE__*/ gql`
  mutation topicsArticleBreakLease($id: ID!) {
    article: contentBreakLease(input: { id: $id }) {
      ... on ContentKnowledgeArticle {
        ...TopicArticle
      }
    }
  }
  ${TopicArticleFragmentDoc}
`;
export type TopicsArticleBreakLeaseMutationFn = Apollo.MutationFunction<
  TopicsArticleBreakLeaseMutation,
  TopicsArticleBreakLeaseMutationVariables
>;
export function useTopicsArticleBreakLeaseMutation(
  baseOptions?: Apollo.MutationHookOptions<TopicsArticleBreakLeaseMutation, TopicsArticleBreakLeaseMutationVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<TopicsArticleBreakLeaseMutation, TopicsArticleBreakLeaseMutationVariables>(
    TopicsArticleBreakLeaseDocument,
    options
  );
}
export type TopicsArticleBreakLeaseMutationHookResult = ReturnType<typeof useTopicsArticleBreakLeaseMutation>;
export type TopicsArticleBreakLeaseMutationOptions = Apollo.BaseMutationOptions<
  TopicsArticleBreakLeaseMutation,
  TopicsArticleBreakLeaseMutationVariables
>;
