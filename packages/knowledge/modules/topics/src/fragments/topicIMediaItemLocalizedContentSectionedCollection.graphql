fragment TopicIMediaItemContentSectionedCollection on IMediaItemLocalizedContentSectionedCollection {
  locale
  value {
    items {
      id
      ... on MediaFile {
        name
        title {
          locale
          text
        }
        path
        type
      }

      ... on MediaFolder {
        name
        title {
          locale
          text
        }
        path
      }
    }

    sections {
      title
      items {
        id
        ... on MediaFile {
          name
          title {
            locale
            text
          }
          path
          type
        }

        ... on MediaFolder {
          name
          title {
            locale
            text
          }
          path
        }
      }
    }
  }
}
